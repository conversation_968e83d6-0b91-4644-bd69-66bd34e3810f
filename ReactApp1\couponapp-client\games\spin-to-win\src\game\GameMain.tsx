import { TryAgainScreen, RewardScreen, OutOfLivesScreen } from './Scenes'
import { GameRuntimeComponentProps } from '@repo/shared/lib/game/game'
import { useGame, GameContext } from '@repo/shared-game-utils/hooks/useGame'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { gameEvents } from '@repo/shared/lib/campaign/eventEmitter'
import { useGameAtom } from '@repo/shared/lib/atoms/atom-extensions'
import { defaultGameConfig, ReactGameConfig } from '../types/config'
import { GameScreenId } from '../types/screen'
import { getBackgroundStyle } from '@repo/shared-game-utils/utils/gameStyleUtils'
import { CounterElement } from '@repo/shared-game-utils/components/CounterElement'
import { GameSoundSwitch } from '@repo/shared-game-utils/components/GameSoundSwitch'
import { GameButton } from '@repo/shared-game-utils/components/GameButton'
import { makeElementsInteractive } from '@repo/shared-game-utils/utils/previewInteractive'
import { usePreloadImage } from '@repo/shared-game-utils/hooks/usePreloadImage'
import { useMusic, useSoundEffect } from '@repo/shared-game-utils/hooks/useSounds'
import { useGameRewards } from '@repo/shared/lib/game/useGameRewards'
import { ReactWheel, WheelItem } from './components/ReactWheel'
import { useGameRewardsDetails, useRewardSet } from "@repo/shared/lib/game/useGameRewardsDetails"
import { RewardRoll, RewardsProvider, useRewards } from '@repo/shared/lib/rewards/index'
import { mockRewardPool } from '@repo/shared/lib/rewards/RewardsManager'

export type GameRuntimeProps = GameRuntimeComponentProps<ReactGameConfig> & {
    currentScreenId?: GameScreenId
    initialGameScreenChecked: React.MutableRefObject<boolean>
}

export default function MainGame(props: GameRuntimeProps) {
    const [currentScreenId, setCurrentScreenId] = useState<GameScreenId>(props.currentScreenId || 'main')
    const initialGameScreenChecked = useRef(false)

    // Create an enhanced context with both props and screen state
    const contextValue = {
        ...props,
        currentScreenId,
        setCurrentScreenId,
        initialGameScreenChecked,
        defaultConfig: defaultGameConfig,
    }

    useEffect(() => {
        if (!props.isPreview) return
        setCurrentScreenId(props.currentScreenId || 'main')
    }, [props.currentScreenId, props.isPreview])

    if (!props.config) {
        console.log("Config not yet there. Waiting...")
        return <>Loading</>
    }

    return (
        <GameContext.Provider value={contextValue}>
            <RewardsProvider gameWidgetId='12345' rewardMechanics={{triggerOn: 'round_start'}}>
            <GameContent />
            </RewardsProvider>
        </GameContext.Provider>
    )
}

export const useGameState = () => {
    const { config, widgetId } = useGame<ReactGameConfig>()
    const initialLivesCount = config.gameEndHandler?.useLives ? config.gameEndHandler?.livesCount || 3 : Number.MAX_SAFE_INTEGER

    const [lives, setLivesCount] = useGameAtom(widgetId, 'livesCount', initialLivesCount)
    const [attemptsTaken, setAttemptsTaken] = useGameAtom(widgetId, 'attemptsTaken', 0)

    return {
        lives,
        attemptsTaken,
        setLivesCount,
        setAttemptsTaken,
    }
}

function usePrepareAssets() {
    console.log("Prepare assets")
    const { config } = useGame<ReactGameConfig>()

    const [loadedElementsCount, setLoadedElementsCount] = useState(0)
    const elementsToLoad = 7
    const loadPercentage = Math.round((loadedElementsCount / elementsToLoad) * 100)

    function appendLoadedElementCount() {
        setLoadedElementsCount((prev) => prev + 1)
    }

    // Preload sounds
    useMusic(config.backgroundMusic, false)
    useMusic(config.gameOverSound, false)
    useMusic(config.winSound, false)
    useMusic(config.spinSound, false)

    // Preload images
    usePreloadImage(config.gameOverOverlay?.asset, appendLoadedElementCount)
    usePreloadImage(config.rewardOverlay?.asset, appendLoadedElementCount)
    usePreloadImage(config.outOfLivesOverlay?.asset, appendLoadedElementCount)
    usePreloadImage(config.gameSoundSwitch?.onAsset, appendLoadedElementCount)
    usePreloadImage(config.gameSoundSwitch?.offAsset, appendLoadedElementCount)
    usePreloadImage(config.spinButton?.asset, appendLoadedElementCount)
    usePreloadImage(config.spinWheel?.overlay?.asset, appendLoadedElementCount)

    return { loadPercentage }
}

function GameContent() {
    const { config, widgetId, resolveAssetUrl, isPreview, currentScreenId, setCurrentScreenId, onGameAssetSelect } = useGame<ReactGameConfig>()

    // Preload assets
    const { loadPercentage } = usePrepareAssets()

    useMusic(config.backgroundMusic, true, true)

    // Create a ref for the container
    const containerRef = useRef<HTMLDivElement>(null)

    // // Function to handle try again button click
    const handleTryAgainClick = useCallback(() => {
        if (isPreview) return

        setCurrentScreenId('main')
    }, [isPreview, setCurrentScreenId])

    // Function to handle reward button click
    const handleRewardButtonClick = useCallback(() => {
        if (isPreview) return

        gameEvents.emit('GameFinished', {
            score: 0, // Set score to 0 since we're not using scores
            widgetId: widgetId,
        })
    }, [isPreview, widgetId])

    // // Function to handle out of lives button click
    const handleOutOfLivesButtonClick = useCallback(() => {
        if (isPreview) return

        gameEvents.emit('GameFinished', {
            score: 0, // Set score to 0 since we're not using scores
            widgetId: widgetId,
        })
    }, [isPreview, widgetId, setCurrentScreenId])

    useEffect(() => {
        if (!isPreview) return
        if (loadPercentage < 100) return

        makeElementsInteractive(onGameAssetSelect)
    }, [currentScreenId, isPreview, loadPercentage])

    if (loadPercentage < 100) {
        return (
            <div className="flex flex-col items-center justify-center h-full w-full">
                <div className="text-xl mb-4">Loading: {loadPercentage}%</div>
                <div className="w-64 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div
                        className="h-full bg-blue-500 transition-all duration-200 ease-linear"
                        style={{ width: `${loadPercentage}%` }}
                    ></div>
                </div>
            </div>
        )
    }

    return (
        <div
            data-game-widget-id={widgetId}
            ref={containerRef}
            className="flex flex-col items-center justify-center h-full w-full touch-none select-none overscroll-none p-4"
            style={getBackgroundStyle(config.mainBackground, resolveAssetUrl)}
        >
            <div className="relative w-full h-full text-black">
                {currentScreenId === 'main' && <MainGameScreen />}
                {currentScreenId === 'try-again' && <TryAgainScreen onButtonClick={handleTryAgainClick} />}
                {currentScreenId === 'claim-reward' && <RewardScreen onButtonClick={handleRewardButtonClick} />}
                {currentScreenId === 'out-of-lives' && <OutOfLivesScreen onButtonClick={handleOutOfLivesButtonClick} />}
            </div>
        </div>
    )
}

function MainGameScreen() {
    const { initialGameScreenChecked, config, setCurrentScreenId, isPreview, widgetId, resolveAssetUrl } = useGame<ReactGameConfig> ()
    const { lives, setLivesCount, attemptsTaken, setAttemptsTaken } = useGameState()
    // const { pickRewardAtAttempt, hasWonReward } = useGameRewards({ gameWidgetId: widgetId, attempt: attemptsTaken, cacheStrategy: 'per-attempt' })
    const {handleGameEvent, getPickResultByRoundId} = useRewards()

    const { rewards } = mockRewardPool//useRewardSet("test")

    const [lastPickResult, setLastPickResult] = useState<{ hasWon: boolean } | null>(null)
    const [isSpinning, setIsSpinning] = useState(false)
    const [spinResult, setSpinResult] = useState<number | null>(null)

    const { play: playSpinSound } = useSoundEffect(config.spinSound)
    const { play: playWinSound } = useSoundEffect(config.winSound)

    const rewardsEnabled = config.gameRewardsHandler?.rewardsEnabled || false
    const useLives = config.gameEndHandler?.useLives

    const [currentReward, setCurrentReward] = useState<any>(null)

    const roundId = "round_123" 


    useEffect(() => {
        handleGameEvent('round_start', { roundId })
    }, [])

    useEffect(() => {
        (async () => {
            const reward = await getPickResultByRoundId(roundId)
            setCurrentReward(reward)
        })()
    }, [roundId])


    const checkInitialGameScreen = useCallback(
        (lives: number) => {
            if (isPreview) return

            // if (rewardsEnabled /*&& hasWonReward*/) {
                // setCurrentScreenId('claim-reward')
                // return
            // }

            // Handle lives system
            if (useLives && lives <= 0) {
                setCurrentScreenId('out-of-lives')
                return
            }
        },
        [isPreview, rewardsEnabled,  useLives, lives, setCurrentScreenId]
    )


    // Reset function is handled automatically by the game lifecycle

    useEffect(() => {
        if (isPreview) return
        if (initialGameScreenChecked.current) return
        console.log('checking initial game screen')
        initialGameScreenChecked.current = true
        checkInitialGameScreen(lives)
    }, [lives])

    const handleSpinEnd = useCallback(() => {
        setIsSpinning(false)
        if (lastPickResult?.hasWon) {
            playWinSound()
            setCurrentScreenId('claim-reward')
        } else {
            setCurrentScreenId('try-again')
        }
    }, [lastPickResult, playWinSound])

    const rewardsData: WheelItem[] = useMemo(() => {
        // First, map the rewards to wheel data
        const rewardItems: WheelItem[] = rewards?.map((reward: any) => {
            // Get custom style for this reward if available, or use default
            const customStyle = config.spinWheel?.rewardStyles?.[reward.id] || {
                backgroundColor: "#f5cb5c",
                textColor: '#000000',
                useImage: false
            }

            // Create the base reward data
            const rewardData: WheelItem = {
                id: reward.id,
                isBlankSlice: false,
                option: reward.name,
                style: {
                    backgroundColor: customStyle.backgroundColor,
                    textColor: customStyle.textColor,
                }
            }

            // Add image only if useImage is true and the reward has an image
            if (customStyle.useImage === true && reward.image) {
                return {
                    ...rewardData,
                    image: {
                        uri: resolveAssetUrl(reward.image),
                        landscape: true,
                    }
                }
            }

            return rewardData
        }) || []

        // Create blank slices if configured
        const blankSlices = config.spinWheel?.blankSlices
        const blankSlicesArray: WheelItem[] = []

        if (blankSlices && blankSlices.count > 0) {
            for (let i = 0; i < blankSlices.count; i++) {
                // Create the base blank slice data
                const blankSliceData = {
                    option: blankSlices.text,
                    style: {
                        backgroundColor: blankSlices.backgroundColor,
                        textColor: blankSlices.textColor,
                    },
                    isBlankSlice: true,
                    id: "blank-slice"
                }

                // Add image if the blank slice has an asset
                if (resolveAssetUrl(blankSlices.asset) != null) {
                    blankSlicesArray.push({
                        ...blankSliceData,
                        image: {
                            uri: resolveAssetUrl(blankSlices.asset),
                            landscape: true,
                        }
                    })
                } else {
                    blankSlicesArray.push(blankSliceData)
                }
            }
        }

        // Interleave reward slices and blank slices for better distribution
        const interleavedData = []

        if (rewardItems.length > 0 && blankSlicesArray.length > 0) {
            // Calculate how many blank slices to place between each reward
            // We want to distribute them evenly, with one blank slice followed by one normal slice
            const totalSlices = rewardItems.length + blankSlicesArray.length
            let rewardIndex = 0
            let blankIndex = 0

            // Alternate between reward and blank slices
            for (let i = 0; i < totalSlices; i++) {
                if (i % 2 === 0) {
                    // Add a reward slice if available
                    if (rewardIndex < rewardItems.length) {
                        interleavedData.push(rewardItems[rewardIndex])
                        rewardIndex++
                    } else if (blankIndex < blankSlicesArray.length) {
                        // If we've used all rewards but still have blanks, add them
                        interleavedData.push(blankSlicesArray[blankIndex])
                        blankIndex++
                    }
                } else {
                    // Add a blank slice if available
                    if (blankIndex < blankSlicesArray.length) {
                        interleavedData.push(blankSlicesArray[blankIndex])
                        blankIndex++
                    } else if (rewardIndex < rewardItems.length) {
                        // If we've used all blanks but still have rewards, add them
                        interleavedData.push(rewardItems[rewardIndex])
                        rewardIndex++
                    }
                }
            }

            return interleavedData
        } else {
            // If there are no blank slices or no reward items, just return the reward items
            return rewardItems
        }
    }, [rewards, config])



    // Function to handle spinning the wheel
    const handleSpin = useCallback(async () => {
        if (isPreview || isSpinning) return

        // Play the spin sound immediately when button is clicked
        playSpinSound()

        // Increment attempts taken
        // setAttemptsTaken((prev: number) => (prev ?? 0) + 1)

        // if (useLives) {
            // setLivesCount(lives - 1)
        // }

        let pickResult: RewardRoll  = null
        if(rewardsEnabled) {
            pickResult = await getPickResultByRoundId(roundId)
            console.log("Pick result", pickResult)
        }

        setIsSpinning(true)
        // setLastPickResult(pickResult);

        // Find the index of the winning reward
        const rewardIndex = rewardsData.findIndex((r: WheelItem) => {
            return pickResult?.hasWon && pickResult?.reward ? r.id === pickResult?.reward?.id : r.isBlankSlice;
        });

        console.log("rewardsData", rewardsData)
        console.log("Reward index", rewardIndex)
        setSpinResult(rewardIndex);

    }, [
        isPreview,
        isSpinning,
        setAttemptsTaken,
        useLives,
        lives,
        setLivesCount,
        rewardsData,
        spinResult,
        // pickRewardAtAttempt,
        playSpinSound,
        setSpinResult,
        setIsSpinning,
        setLastPickResult
    ])

    return (
        <div className="game-content-area w-full h-full flex flex-col items-center justify-center">
            {/* Top section with sound switch and lives counter using three flex containers */}
            <div className="w-full flex items-center">
                {/* Left container */}
                <div className="flex-1 flex justify-start items-center p-2">
                    {useLives && (config.livesStyle?.position === 'left' || (!config.livesStyle?.position && true)) && (
                        <CounterElement configKey="livesStyle" style={config.livesStyle}>
                            {lives}
                        </CounterElement>
                    )}
                    {(config.gameSoundSwitch?.position === 'left' || (config.gameSoundSwitch?.alignment === 'left' && !config.gameSoundSwitch?.position)) && (
                        <GameSoundSwitch config={config.gameSoundSwitch} dataConfigKey="gameSoundSwitch" />
                    )}
                </div>

                {/* Center container */}
                <div className="flex-1 flex justify-center items-center p-2">
                    {useLives && config.livesStyle?.position === 'center' && (
                        <CounterElement configKey="livesStyle" style={config.livesStyle}>
                            {lives}
                        </CounterElement>
                    )}
                    {(config.gameSoundSwitch?.position === 'center' || (config.gameSoundSwitch?.alignment === 'center' && !config.gameSoundSwitch?.position)) && (
                        <GameSoundSwitch config={config.gameSoundSwitch} dataConfigKey="gameSoundSwitch" />
                    )}
                </div>

                {/* Right container */}
                <div className="flex-1 flex justify-end items-center p-2">
                    {useLives && config.livesStyle?.position === 'right' && (
                        <CounterElement configKey="livesStyle" style={config.livesStyle}>
                            {lives}
                        </CounterElement>
                    )}
                    {(config.gameSoundSwitch?.position === 'right' || (config.gameSoundSwitch?.alignment === 'right' && !config.gameSoundSwitch?.position)) && (
                        <GameSoundSwitch config={config.gameSoundSwitch} dataConfigKey="gameSoundSwitch" />
                    )}
                </div>
            </div>



            {/* Spin Wheel */}
            <div className={"flex-grow flex flex-col items-center justify-center relative " + (isPreview ? '' : 'pointer-events-none')} >
                <ReactWheel
                    mustStartSpinning={isSpinning}
                    targetIndex={spinResult}
                    data={rewardsData}
                    spinDuration={config.spinWheel?.spinDuration || 6}
                    size={config.spinWheel?.size || 450}
                    isPreviewMode={isPreview}
                    onStopSpinning={() => {
                            handleSpinEnd()
                    }}
                    pointerProps={{
                        src: resolveAssetUrl(config.spinWheel?.pointer?.asset),
                        position: config.spinWheel?.pointer?.position || 'top-center',
                        style: {
                            width: '80px',
                            height: '80px',
                            imageRendering: config.spinWheel?.pointer?.backgroundScaleMode?.includes('pixelated') ? 'pixelated' : 'auto',
                            transform: `translateX(${config.spinWheel?.pointer?.offsetX || 0}px) translateY(${config.spinWheel?.pointer?.offsetY || 0}px)`,
                        }
                    }}
                    borderProps={{
                        width: config.spinWheel?.border?.width || 4,
                        color: config.spinWheel?.border?.color || '#000000',
                        enabled: config.spinWheel?.border?.enabled !== undefined ? config.spinWheel.border.enabled : true
                    }}
                    dividerProps={{
                        width: config.spinWheel?.dividers?.width || 2,
                        color: config.spinWheel?.dividers?.color || '#ffffff',
                        enabled: config.spinWheel?.dividers?.enabled !== undefined ? config.spinWheel.dividers.enabled : true
                    }}
                    overlayProps={{
                        src: resolveAssetUrl(config.spinWheel?.overlay?.asset),
                        enabled: config.spinWheel?.overlay?.enabled || false,
                        style: {
                            imageRendering: config.spinWheel?.overlay?.backgroundScaleMode?.includes('pixelated') ? 'pixelated' : 'auto',
                            left: `calc(50% + ${config.spinWheel?.overlay?.offsetX || 0}px)`,
                            top: `calc(50% + ${config.spinWheel?.overlay?.offsetY || 0}px)`,
                            transform: `translate(-50%, -50%) scale(${config.spinWheel?.overlay?.scale || 1})`,
                        }
                    }}
                />
            </div>
            <div className={"mt-8" + (isSpinning ? ' opacity-50 pointer-events-none' : '')}>
                <GameButton
                    config={config.spinButton}
                    onClick={handleSpin}
                    dataConfigKey="spinButton"
                />
            </div>

        </div>
    )
}
